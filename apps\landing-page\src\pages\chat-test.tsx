import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSession, signIn } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useTheme } from '@/themes';

// Real AI Message types (matching the API)
interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  confidence?: number;
  extractedData?: any;
}

interface AISession {
  id: string;
  currentStep: string;
  status: string;
  userRole: 'CLIENT' | 'EXPERT';
  messages: AIMessage[];
  extractedData: any;
  completionRate: number;
}

// API Response types
interface SendMessageResponse {
  success: boolean;
  message: string;
  data?: {
    aiMessage: AIMessage;
    extractedData: any;
    isCompleted: boolean;
    nextStep?: string;
    completionRate: number;
  };
  error?: string;
}

interface StartConversationResponse {
  success: boolean;
  message: string;
  data?: {
    sessionId: string;
    currentStep: string;
    messages: AIMessage[];
    extractedData: any;
    status: string;
    completionRate: number;
  };
  error?: string;
}

// Message Bubble Component
const MessageBubble: React.FC<{ message: AIMessage; isLast: boolean }> = ({ message, isLast }) => {
  const isUser = message.role === 'user';
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}
    >
      <div className={`flex items-end space-x-2 rtl:space-x-reverse max-w-[80%]`}>
        {!isUser && (
          <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
            </svg>
          </div>
        )}

        {/* Message Content */}
        <div
          className={`
            px-4 py-3 rounded-2xl shadow-sm backdrop-blur-sm
            ${isUser
              ? 'bg-primary-500 text-white rounded-br-md'
              : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-600'
            }
          `}
        >
          <p className="text-sm leading-relaxed whitespace-pre-wrap font-cairo">
            {message.content}
          </p>
          
          {/* Timestamp */}
          <div className={`text-xs mt-2 ${isUser ? 'text-primary-100' : 'text-gray-500 dark:text-gray-400'}`}>
            {new Date(message.timestamp).toLocaleTimeString('ar-SA', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
        </div>

        {isUser && (
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        )}
      </div>
    </motion.div>
  );
};

// Typing Indicator Component
const TypingIndicator: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="flex justify-start mb-4"
    >
      <div className="flex items-end space-x-2 rtl:space-x-reverse">
        <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
          </svg>
        </div>
        <div className="bg-white dark:bg-gray-700 rounded-2xl rounded-bl-md px-4 py-3 border border-gray-200 dark:border-gray-600">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Main Chat Test Component
const ChatTestPage: React.FC = () => {
  const { currentTheme } = useTheme();
  const { data: session, status } = useSession();
  const router = useRouter();

  // State management
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [currentSession, setCurrentSession] = useState<AISession | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Initialize AI session when user is authenticated
  useEffect(() => {
    const initializeSession = async () => {
      if (status === 'loading') return;

      if (!session?.user) {
        setIsInitializing(false);
        return;
      }

      try {
        setIsInitializing(true);
        setError(null);

        // Start a new AI conversation session
        const response = await fetch('/api/ai/conversation/start', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userRole: 'EXPERT', // Default for testing, can be made dynamic
            language: 'ar',
            sessionType: 'onboarding',
            culturalContext: {
              location: 'Syria',
              dialect: 'general'
            }
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to start conversation: ${response.status}`);
        }

        const data: StartConversationResponse = await response.json();

        if (data.success && data.data) {
          const sessionData: AISession = {
            id: data.data.sessionId,
            currentStep: data.data.currentStep,
            status: data.data.status,
            userRole: 'EXPERT',
            messages: data.data.messages || [],
            extractedData: data.data.extractedData || {},
            completionRate: data.data.completionRate || 0
          };

          setCurrentSession(sessionData);
          setMessages(sessionData.messages);
        } else {
          throw new Error(data.error || 'Failed to initialize session');
        }
      } catch (error: any) {
        console.error('Failed to initialize AI session:', error);
        setError(error.message);
      } finally {
        setIsInitializing(false);
      }
    };

    initializeSession();
  }, [session, status]);



  // Real send message function with API integration
  const sendMessage = async () => {
    if (!inputValue.trim() || isSending) return;

    const userMessage: AIMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsSending(true);
    setIsTyping(true);
    setError(null);

    try {
      if (!currentSession) {
        throw new Error('No active session found');
      }

      // Real API call
      const response = await fetch('/api/ai/conversation/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: currentSession.id,
          message: inputValue.trim(),
          messageType: 'text'
        }),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data: SendMessageResponse = await response.json();

      if (data.success && data.data) {
        const aiMessage: AIMessage = {
          id: data.data.aiMessage.id,
          role: 'assistant',
          content: data.data.aiMessage.content,
          timestamp: data.data.aiMessage.timestamp,
          confidence: data.data.aiMessage.confidence,
          extractedData: data.data.aiMessage.extractedData
        };

        setMessages(prev => [...prev, aiMessage]);

        // Update session data
        if (currentSession) {
          setCurrentSession({
            ...currentSession,
            messages: [...messages, userMessage, aiMessage],
            extractedData: { ...currentSession.extractedData, ...data.data.extractedData },
            completionRate: data.data.completionRate
          });
        }

        // Check if conversation is completed
        if (data.data.isCompleted) {
          // Handle completion - could redirect to dashboard
          console.log('Conversation completed!', data.data.extractedData);
        }
      } else {
        throw new Error(data.error || 'Failed to get AI response');
      }
    } catch (error: any) {
      console.error('Failed to send message:', error);
      setError(error.message);

      // Add error message to chat
      const errorMessage: AIMessage = {
        id: `error_${Date.now()}`,
        role: 'assistant',
        content: 'عذراً، حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.',
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
      setIsSending(false);
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Authentication check
  if (status === 'loading' || isInitializing) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo flex items-center justify-center" dir="rtl">
        <div className="glass-theme rounded-2xl p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-white mb-2">جاري التحميل...</h2>
          <p className="text-gray-300">
            {status === 'loading' ? 'جاري التحقق من الجلسة...' : 'جاري تهيئة المحادثة الذكية...'}
          </p>
        </div>
      </div>
    );
  }

  if (!session?.user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo flex items-center justify-center" dir="rtl">
        <div className="glass-theme rounded-2xl p-8 text-center max-w-md">
          <h2 className="text-2xl font-bold text-white mb-4">تسجيل الدخول مطلوب</h2>
          <p className="text-gray-300 mb-6">
            يجب تسجيل الدخول للوصول إلى واجهة الدردشة الذكية
          </p>
          <button
            type="button"
            onClick={() => signIn('google')}
            className="w-full px-6 py-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 transition-colors font-semibold"
          >
            تسجيل الدخول بجوجل
          </button>

        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo" dir="rtl">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            الدردشة الذكية - فريلا سوريا
          </h1>
          <p className="text-gray-300">
            محادثة حقيقية مع الذكاء الاصطناعي لإعداد ملفك الشخصي
          </p>
          {session?.user && (
            <p className="text-sm text-gray-400 mt-2">
              مرحباً {session.user.name} ({session.user.email})
            </p>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-xl text-red-200">
            <h3 className="font-semibold mb-2">حدث خطأ:</h3>
            <p>{error}</p>
            <button
              type="button"
              onClick={() => {
                setError(null);
                window.location.reload();
              }}
              className="mt-3 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              إعادة المحاولة
            </button>
          </div>
        )}

        {/* Chat Container */}
        <div className="glass-theme rounded-2xl overflow-hidden shadow-theme-lg max-w-2xl mx-auto">
          {/* Chat Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary-500/10 to-primary-600/10">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">مساعد فريلا الذكي</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">متصل الآن</p>
                </div>
              </div>
              
              {/* Progress indicator */}
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {currentSession ? Math.round(currentSession.completionRate * 100) : 0}% مكتمل
              </div>
            </div>
          </div>

          {/* Messages Area */}
          <div className="h-96 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
            {messages.map((message, index) => (
              <MessageBubble
                key={message.id}
                message={message}
                isLast={index === messages.length - 1}
              />
            ))}
            
            <AnimatePresence>
              {isTyping && <TypingIndicator />}
            </AnimatePresence>
            
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-end space-x-3 rtl:space-x-reverse">
              <div className="flex-1">
                <textarea
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="اكتب رسالتك هنا..."
                  className="
                    w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600
                    bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                    focus:ring-2 focus:ring-primary-500 focus:border-transparent
                    resize-none max-h-32 font-cairo
                    placeholder-gray-500 dark:placeholder-gray-400
                  "
                  rows={1}
                  disabled={isSending}
                />
              </div>
              
              <button
                type="button"
                onClick={sendMessage}
                disabled={!inputValue.trim() || isSending}
                className="
                  p-3 bg-primary-500 text-white rounded-xl
                  hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed
                  transition-colors duration-200
                  flex-shrink-0
                "
              >
                {isSending ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
};

export default ChatTestPage;
