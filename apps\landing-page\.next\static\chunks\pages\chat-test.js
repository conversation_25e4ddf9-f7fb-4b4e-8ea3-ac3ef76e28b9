/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/chat-test"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test!":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test! ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/chat-test\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/chat-test.tsx */ \"./src/pages/chat-test.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/chat-test\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9QyUzQSU1Q1VzZXJzJTVDYW1lcmslNUNEb2N1bWVudHMlNUNGcmVlbGElNUNhcHBzJTVDbGFuZGluZy1wYWdlJTVDc3JjJTVDcGFnZXMlNUNjaGF0LXRlc3QudHN4JnBhZ2U9JTJGY2hhdC10ZXN0ISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLDREQUEyQjtBQUNsRDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ZGU4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL2NoYXQtdGVzdFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vc3JjL3BhZ2VzL2NoYXQtdGVzdC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL2NoYXQtdGVzdFwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test!\n"));

/***/ }),

/***/ "./src/pages/chat-test.tsx":
/*!*********************************!*\
  !*** ./src/pages/chat-test.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n// Message Bubble Component\nconst MessageBubble = (param)=>{\n    let { message, isLast } = param;\n    const isUser = message.role === \"user\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: \"flex \".concat(isUser ? \"justify-end\" : \"justify-start\", \" mb-4\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-2 rtl:space-x-reverse max-w-[80%]\",\n            children: [\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\\n            px-4 py-3 rounded-2xl shadow-sm backdrop-blur-sm\\n            \".concat(isUser ? \"bg-primary-500 text-white rounded-br-md\" : \"bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-600\", \"\\n          \"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed whitespace-pre-wrap font-cairo\",\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs mt-2 \".concat(isUser ? \"text-primary-100\" : \"text-gray-500 dark:text-gray-400\"),\n                            children: new Date(message.timestamp).toLocaleTimeString(\"ar-SA\", {\n                                hour: \"2-digit\",\n                                minute: \"2-digit\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MessageBubble;\n// Typing Indicator Component\nconst TypingIndicator = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 10\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -10\n        },\n        className: \"flex justify-start mb-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-2 rtl:space-x-reverse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-700 rounded-2xl rounded-bl-md px-4 py-3 border border-gray-200 dark:border-gray-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0.1s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0.2s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TypingIndicator;\n// Main Chat Test Component\nconst ChatTestPage = ()=>{\n    _s();\n    const { currentTheme } = (0,_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State management\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSending, setIsSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInitializing, setIsInitializing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [testMode, setTestMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isTyping\n    ]);\n    // Focus input on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _inputRef_current;\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    }, []);\n    // Initialize AI session when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeSession = async ()=>{\n            if (status === \"loading\") return;\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                setIsInitializing(false);\n                return;\n            }\n            try {\n                setIsInitializing(true);\n                setError(null);\n                // Start a new AI conversation session\n                const response = await fetch(\"/api/ai/conversation/start\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        userRole: \"EXPERT\",\n                        language: \"ar\",\n                        sessionType: \"onboarding\",\n                        culturalContext: {\n                            location: \"Syria\",\n                            dialect: \"general\"\n                        }\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to start conversation: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success && data.data) {\n                    const sessionData = {\n                        id: data.data.sessionId,\n                        currentStep: data.data.currentStep,\n                        status: data.data.status,\n                        userRole: \"EXPERT\",\n                        messages: data.data.messages || [],\n                        extractedData: data.data.extractedData || {},\n                        completionRate: data.data.completionRate || 0\n                    };\n                    setCurrentSession(sessionData);\n                    setMessages(sessionData.messages);\n                } else {\n                    throw new Error(data.error || \"Failed to initialize session\");\n                }\n            } catch (error) {\n                console.error(\"Failed to initialize AI session:\", error);\n                setError(error.message);\n                // Fall back to test mode\n                setTestMode(true);\n                initializeTestMode();\n            } finally{\n                setIsInitializing(false);\n            }\n        };\n        initializeSession();\n    }, [\n        session,\n        status\n    ]);\n    // Initialize test mode with mock data\n    const initializeTestMode = ()=>{\n        const mockMessages = [\n            {\n                id: \"1\",\n                role: \"assistant\",\n                content: \"مرحباً بك في فريلا سوريا! أنا هنا لمساعدتك في إعداد ملفك الشخصي. هل أنت خبير تريد تقديم خدماتك أم عميل تبحث عن خدمات؟\",\n                timestamp: new Date(Date.now() - 300000).toISOString(),\n                confidence: 0.95\n            },\n            {\n                id: \"2\",\n                role: \"user\",\n                content: \"أنا خبير في تطوير المواقع الإلكترونية\",\n                timestamp: new Date(Date.now() - 240000).toISOString()\n            },\n            {\n                id: \"3\",\n                role: \"assistant\",\n                content: \"ممتاز! تطوير المواقع الإلكترونية مجال مطلوب جداً. يمكنك أن تخبرني أكثر عن خبراتك؟ مثلاً، ما هي التقنيات التي تتقنها؟ React، Vue، Angular؟\",\n                timestamp: new Date(Date.now() - 180000).toISOString(),\n                confidence: 0.92\n            }\n        ];\n        const mockSession = {\n            id: \"test-session-1\",\n            currentStep: \"skills_assessment\",\n            status: \"active\",\n            userRole: \"EXPERT\",\n            messages: mockMessages,\n            extractedData: {},\n            completionRate: 0.3\n        };\n        setCurrentSession(mockSession);\n        setMessages(mockMessages);\n    };\n    // Real send message function with API integration\n    const sendMessage = async ()=>{\n        if (!inputValue.trim() || isSending) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            role: \"user\",\n            content: inputValue.trim(),\n            timestamp: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsSending(true);\n        setIsTyping(true);\n        setError(null);\n        try {\n            if (testMode || !currentSession) {\n                // Fall back to mock response in test mode\n                setTimeout(()=>{\n                    const aiResponses = [\n                        \"شكراً لك على هذه المعلومات المفيدة!\",\n                        \"هذا رائع! يمكنك أن تخبرني أكثر عن خبراتك؟\",\n                        \"ممتاز! ما هي المشاريع التي عملت عليها مؤخراً؟\",\n                        \"أفهم. هل لديك أمثلة على أعمالك السابقة؟\",\n                        \"رائع! كم سنة من الخبرة لديك في هذا المجال؟\"\n                    ];\n                    const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];\n                    const aiMessage = {\n                        id: \"ai_\".concat(Date.now()),\n                        role: \"assistant\",\n                        content: randomResponse,\n                        timestamp: new Date().toISOString(),\n                        confidence: Math.random() * 0.3 + 0.7\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            aiMessage\n                        ]);\n                    setIsTyping(false);\n                    setIsSending(false);\n                }, 2000);\n                return;\n            }\n            // Real API call\n            const response = await fetch(\"/api/ai/conversation/message\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    sessionId: currentSession.id,\n                    message: inputValue.trim(),\n                    messageType: \"text\"\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"API request failed: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (data.success && data.data) {\n                const aiMessage = {\n                    id: data.data.aiMessage.id,\n                    role: \"assistant\",\n                    content: data.data.aiMessage.content,\n                    timestamp: data.data.aiMessage.timestamp,\n                    confidence: data.data.aiMessage.confidence,\n                    extractedData: data.data.aiMessage.extractedData\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n                // Update session data\n                if (currentSession) {\n                    setCurrentSession({\n                        ...currentSession,\n                        messages: [\n                            ...messages,\n                            userMessage,\n                            aiMessage\n                        ],\n                        extractedData: {\n                            ...currentSession.extractedData,\n                            ...data.data.extractedData\n                        },\n                        completionRate: data.data.completionRate\n                    });\n                }\n                // Check if conversation is completed\n                if (data.data.isCompleted) {\n                    // Handle completion - could redirect to dashboard\n                    console.log(\"Conversation completed!\", data.data.extractedData);\n                }\n            } else {\n                throw new Error(data.error || \"Failed to get AI response\");\n            }\n        } catch (error) {\n            console.error(\"Failed to send message:\", error);\n            setError(error.message);\n            // Add error message to chat\n            const errorMessage = {\n                id: \"error_\".concat(Date.now()),\n                role: \"assistant\",\n                content: \"عذراً، حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.\",\n                timestamp: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsTyping(false);\n            setIsSending(false);\n        }\n    };\n    // Handle Enter key press\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    // Authentication check\n    if (status === \"loading\" || isInitializing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo flex items-center justify-center\",\n            dir: \"rtl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-theme rounded-2xl p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-white mb-2\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: status === \"loading\" ? \"جاري التحقق من الجلسة...\" : \"جاري تهيئة المحادثة الذكية...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                lineNumber: 401,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 400,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!(session === null || session === void 0 ? void 0 : session.user)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo flex items-center justify-center\",\n            dir: \"rtl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-theme rounded-2xl p-8 text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"تسجيل الدخول مطلوب\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-6\",\n                        children: \"يجب تسجيل الدخول للوصول إلى واجهة الدردشة الذكية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)(\"google\"),\n                        className: \"w-full px-6 py-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 transition-colors font-semibold\",\n                        children: \"تسجيل الدخول بجوجل\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>{\n                            setTestMode(true);\n                            initializeTestMode();\n                        },\n                        className: \"w-full mt-3 px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors\",\n                        children: \"متابعة في وضع الاختبار\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                lineNumber: 415,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 414,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-white mb-2\",\n                            children: testMode ? \"اختبار واجهة الدردشة الذكية\" : \"الدردشة الذكية - فريلا سوريا\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: testMode ? \"وضع الاختبار - بيانات وهمية\" : \"محادثة حقيقية مع الذكاء الاصطناعي لإعداد ملفك الشخصي\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 11\n                        }, undefined),\n                        (session === null || session === void 0 ? void 0 : session.user) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mt-2\",\n                            children: [\n                                \"مرحباً \",\n                                session.user.name,\n                                \" (\",\n                                session.user.email,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-xl text-red-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold mb-2\",\n                            children: \"حدث خطأ:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>{\n                                setError(null);\n                                setTestMode(true);\n                                initializeTestMode();\n                            },\n                            className: \"mt-3 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors\",\n                            children: \"التبديل إلى وضع الاختبار\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-theme rounded-2xl overflow-hidden shadow-theme-lg max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary-500/10 to-primary-600/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"مساعد فريلا الذكي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"متصل الآن\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            currentSession ? Math.round(currentSession.completionRate * 100) : 0,\n                                            \"% مكتمل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-96 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                            children: [\n                                messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBubble, {\n                                        message: message,\n                                        isLast: index === messages.length - 1\n                                    }, message.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                                    children: isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingIndicator, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 28\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end space-x-3 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: inputRef,\n                                            value: inputValue,\n                                            onChange: (e)=>setInputValue(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"اكتب رسالتك هنا...\",\n                                            className: \" w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none max-h-32 font-cairo placeholder-gray-500 dark:placeholder-gray-400 \",\n                                            rows: 1,\n                                            disabled: isSending\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: sendMessage,\n                                        disabled: !inputValue.trim() || isSending,\n                                        className: \" p-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex-shrink-0 \",\n                                        children: isSending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-theme rounded-xl p-4 max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"عناصر التحكم في الاختبار\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setIsTyping(!isTyping),\n                                        className: \"w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\",\n                                        children: [\n                                            isTyping ? \"إيقاف\" : \"تشغيل\",\n                                            \" مؤشر الكتابة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            if (testMode) {\n                                                initializeTestMode();\n                                            } else if (currentSession) {\n                                                setMessages(currentSession.messages);\n                                            }\n                                        },\n                                        className: \"w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\",\n                                        children: \"إعادة تعيين الرسائل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !testMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setTestMode(true);\n                                            initializeTestMode();\n                                        },\n                                        className: \"w-full px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors\",\n                                        children: \"التبديل إلى وضع الاختبار\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    testMode && (session === null || session === void 0 ? void 0 : session.user) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setTestMode(false);\n                                            setError(null);\n                                            // Re-initialize real session\n                                            window.location.reload();\n                                        },\n                                        className: \"w-full px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors\",\n                                        children: \"العودة إلى الوضع الحقيقي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 444,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 443,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatTestPage, \"/JDJiMMBOyZLQ+rMI9EcIyknDsE=\", false, function() {\n    return [\n        _themes__WEBPACK_IMPORTED_MODULE_4__.useTheme,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c2 = ChatTestPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatTestPage);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MessageBubble\");\n$RefreshReg$(_c1, \"TypingIndicator\");\n$RefreshReg$(_c2, \"ChatTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/chat-test.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);