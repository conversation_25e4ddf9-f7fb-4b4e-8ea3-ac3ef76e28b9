/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/chat-test"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test!":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test! ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/chat-test\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/chat-test.tsx */ \"./src/pages/chat-test.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/chat-test\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9QyUzQSU1Q1VzZXJzJTVDYW1lcmslNUNEb2N1bWVudHMlNUNGcmVlbGElNUNhcHBzJTVDbGFuZGluZy1wYWdlJTVDc3JjJTVDcGFnZXMlNUNjaGF0LXRlc3QudHN4JnBhZ2U9JTJGY2hhdC10ZXN0ISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLDREQUEyQjtBQUNsRDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ZGU4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL2NoYXQtdGVzdFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vc3JjL3BhZ2VzL2NoYXQtdGVzdC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL2NoYXQtdGVzdFwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test!\n"));

/***/ }),

/***/ "./src/pages/chat-test.tsx":
/*!*********************************!*\
  !*** ./src/pages/chat-test.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n// Message Bubble Component\nconst MessageBubble = (param)=>{\n    let { message, isLast } = param;\n    const isUser = message.role === \"user\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: \"flex \".concat(isUser ? \"justify-end\" : \"justify-start\", \" mb-4\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-2 rtl:space-x-reverse max-w-[80%]\",\n            children: [\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\\n            px-4 py-3 rounded-2xl shadow-sm backdrop-blur-sm\\n            \".concat(isUser ? \"bg-primary-500 text-white rounded-br-md\" : \"bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-600\", \"\\n          \"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed whitespace-pre-wrap font-cairo\",\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs mt-2 \".concat(isUser ? \"text-primary-100\" : \"text-gray-500 dark:text-gray-400\"),\n                            children: new Date(message.timestamp).toLocaleTimeString(\"ar-SA\", {\n                                hour: \"2-digit\",\n                                minute: \"2-digit\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MessageBubble;\n// Typing Indicator Component\nconst TypingIndicator = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 10\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -10\n        },\n        className: \"flex justify-start mb-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-2 rtl:space-x-reverse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-700 rounded-2xl rounded-bl-md px-4 py-3 border border-gray-200 dark:border-gray-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0.1s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0.2s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TypingIndicator;\n// Main Chat Test Component\nconst ChatTestPage = ()=>{\n    _s();\n    const { currentTheme } = (0,_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State management\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSending, setIsSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInitializing, setIsInitializing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isTyping\n    ]);\n    // Focus input on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _inputRef_current;\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    }, []);\n    // Initialize AI session when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeSession = async ()=>{\n            if (status === \"loading\") return;\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                setIsInitializing(false);\n                return;\n            }\n            try {\n                setIsInitializing(true);\n                setError(null);\n                // Start a new AI conversation session\n                const response = await fetch(\"/api/ai/conversation/start\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        userRole: \"EXPERT\",\n                        language: \"ar\",\n                        sessionType: \"onboarding\",\n                        culturalContext: {\n                            location: \"Syria\",\n                            dialect: \"general\"\n                        }\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to start conversation: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success && data.data) {\n                    const sessionData = {\n                        id: data.data.sessionId,\n                        currentStep: data.data.currentStep,\n                        status: data.data.status,\n                        userRole: \"EXPERT\",\n                        messages: data.data.messages || [],\n                        extractedData: data.data.extractedData || {},\n                        completionRate: data.data.completionRate || 0\n                    };\n                    setCurrentSession(sessionData);\n                    setMessages(sessionData.messages);\n                } else {\n                    throw new Error(data.error || \"Failed to initialize session\");\n                }\n            } catch (error) {\n                console.error(\"Failed to initialize AI session:\", error);\n                setError(error.message);\n            } finally{\n                setIsInitializing(false);\n            }\n        };\n        initializeSession();\n    }, [\n        session,\n        status\n    ]);\n    // Real send message function with API integration\n    const sendMessage = async ()=>{\n        if (!inputValue.trim() || isSending) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            role: \"user\",\n            content: inputValue.trim(),\n            timestamp: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsSending(true);\n        setIsTyping(true);\n        setError(null);\n        try {\n            if (!currentSession) {\n                throw new Error(\"No active session found\");\n            }\n            // Real API call\n            const response = await fetch(\"/api/ai/conversation/message\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    sessionId: currentSession.id,\n                    message: inputValue.trim(),\n                    messageType: \"text\"\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"API request failed: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (data.success && data.data) {\n                const aiMessage = {\n                    id: data.data.aiMessage.id,\n                    role: \"assistant\",\n                    content: data.data.aiMessage.content,\n                    timestamp: data.data.aiMessage.timestamp,\n                    confidence: data.data.aiMessage.confidence,\n                    extractedData: data.data.aiMessage.extractedData\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n                // Update session data\n                if (currentSession) {\n                    setCurrentSession({\n                        ...currentSession,\n                        messages: [\n                            ...messages,\n                            userMessage,\n                            aiMessage\n                        ],\n                        extractedData: {\n                            ...currentSession.extractedData,\n                            ...data.data.extractedData\n                        },\n                        completionRate: data.data.completionRate\n                    });\n                }\n                // Check if conversation is completed\n                if (data.data.isCompleted) {\n                    // Handle completion - could redirect to dashboard\n                    console.log(\"Conversation completed!\", data.data.extractedData);\n                }\n            } else {\n                throw new Error(data.error || \"Failed to get AI response\");\n            }\n        } catch (error) {\n            console.error(\"Failed to send message:\", error);\n            setError(error.message);\n            // Add error message to chat\n            const errorMessage = {\n                id: \"error_\".concat(Date.now()),\n                role: \"assistant\",\n                content: \"عذراً، حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.\",\n                timestamp: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsTyping(false);\n            setIsSending(false);\n        }\n    };\n    // Handle Enter key press\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    // Authentication check\n    if (status === \"loading\" || isInitializing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo flex items-center justify-center\",\n            dir: \"rtl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-theme rounded-2xl p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-white mb-2\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: status === \"loading\" ? \"جاري التحقق من الجلسة...\" : \"جاري تهيئة المحادثة الذكية...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 335,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!(session === null || session === void 0 ? void 0 : session.user)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo flex items-center justify-center\",\n            dir: \"rtl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-theme rounded-2xl p-8 text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"تسجيل الدخول مطلوب\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-6\",\n                        children: \"يجب تسجيل الدخول للوصول إلى واجهة الدردشة الذكية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)(\"google\"),\n                        className: \"w-full px-6 py-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 transition-colors font-semibold\",\n                        children: \"تسجيل الدخول بجوجل\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                lineNumber: 350,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 349,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-white mb-2\",\n                            children: \"الدردشة الذكية - فريلا سوريا\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: \"محادثة حقيقية مع الذكاء الاصطناعي لإعداد ملفك الشخصي\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 11\n                        }, undefined),\n                        (session === null || session === void 0 ? void 0 : session.user) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mt-2\",\n                            children: [\n                                \"مرحباً \",\n                                session.user.name,\n                                \" (\",\n                                session.user.email,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-xl text-red-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold mb-2\",\n                            children: \"حدث خطأ:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>{\n                                setError(null);\n                                window.location.reload();\n                            },\n                            className: \"mt-3 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors\",\n                            children: \"إعادة المحاولة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-theme rounded-2xl overflow-hidden shadow-theme-lg max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary-500/10 to-primary-600/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"مساعد فريلا الذكي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"متصل الآن\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            currentSession ? Math.round(currentSession.completionRate * 100) : 0,\n                                            \"% مكتمل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-96 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                            children: [\n                                messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBubble, {\n                                        message: message,\n                                        isLast: index === messages.length - 1\n                                    }, message.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                                    children: isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingIndicator, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 28\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end space-x-3 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: inputRef,\n                                            value: inputValue,\n                                            onChange: (e)=>setInputValue(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"اكتب رسالتك هنا...\",\n                                            className: \" w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none max-h-32 font-cairo placeholder-gray-500 dark:placeholder-gray-400 \",\n                                            rows: 1,\n                                            disabled: isSending\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: sendMessage,\n                                        disabled: !inputValue.trim() || isSending,\n                                        className: \" p-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex-shrink-0 \",\n                                        children: isSending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 370,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 369,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatTestPage, \"62jkvfzUhTD3I9MgBz+PwXG6cCk=\", false, function() {\n    return [\n        _themes__WEBPACK_IMPORTED_MODULE_4__.useTheme,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c2 = ChatTestPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatTestPage);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MessageBubble\");\n$RefreshReg$(_c1, \"TypingIndicator\");\n$RefreshReg$(_c2, \"ChatTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/chat-test.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);