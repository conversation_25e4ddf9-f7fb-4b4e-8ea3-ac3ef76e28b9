{"name": "@freela/landing-page", "version": "1.0.0", "description": "<PERSON>la Syria Landing Page - Next.js", "private": true, "scripts": {"dev": "next dev -p 3006", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@freela/i18n": "file:../../packages/i18n", "@freela/types": "file:../../packages/types", "@freela/utils": "file:../../packages/utils", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@next/font": "14.0.3", "@prisma/client": "^6.9.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "i18next": "^23.7.6", "next": "14.0.3", "next-auth": "^4.24.5", "next-i18next": "^15.2.0", "next-seo": "^6.4.0", "next-themes": "^0.2.1", "postcss": "^8.4.32", "react": "18.2.0", "react-dom": "18.2.0", "react-google-button": "^0.7.2", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-intersection-observer": "^9.5.3", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.6", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@types/node": "^20.9.2", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^9.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.1.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}