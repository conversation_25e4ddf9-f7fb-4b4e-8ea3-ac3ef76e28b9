/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/chat-test";
exports.ids = ["pages/chat-test"];
exports.modules = {

/***/ "__barrel_optimize__?names=ChevronDownIcon,CommandLineIcon,SparklesIcon,SwatchIcon,XMarkIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronDownIcon,CommandLineIcon,SparklesIcon,SwatchIcon,XMarkIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronDownIcon: () => (/* reexport safe */ _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CommandLineIcon: () => (/* reexport safe */ _CommandLineIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   SparklesIcon: () => (/* reexport safe */ _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   SwatchIcon: () => (/* reexport safe */ _SwatchIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChevronDownIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _CommandLineIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CommandLineIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/CommandLineIcon.js\");\n/* harmony import */ var _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SparklesIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _SwatchIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SwatchIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/SwatchIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./XMarkIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGV2cm9uRG93bkljb24sQ29tbWFuZExpbmVJY29uLFNwYXJrbGVzSWNvbixTd2F0Y2hJY29uLFhNYXJrSWNvbiE9IS4uLy4uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ2lFO0FBQ0E7QUFDTjtBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZyZWVsYS9sYW5kaW5nLXBhZ2UvLi4vLi4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/Njk0YSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvbkRvd25JY29uIH0gZnJvbSBcIi4vQ2hldnJvbkRvd25JY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29tbWFuZExpbmVJY29uIH0gZnJvbSBcIi4vQ29tbWFuZExpbmVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3BhcmtsZXNJY29uIH0gZnJvbSBcIi4vU3BhcmtsZXNJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3dhdGNoSWNvbiB9IGZyb20gXCIuL1N3YXRjaEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYTWFya0ljb24gfSBmcm9tIFwiLi9YTWFya0ljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ChevronDownIcon,CommandLineIcon,SparklesIcon,SwatchIcon,XMarkIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat-test&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cchat-test.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat-test&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cchat-test.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\chat-test.tsx */ \"./src/pages/chat-test.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/chat-test\",\n        pathname: \"/chat-test\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_chat_test_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat-test&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cchat-test.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        // Update state so the next render will show the fallback UI\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        // Filter out known extension errors that we can't control\n        const isExtensionError = error.message?.includes(\"Frame with ID\") || error.message?.includes(\"Could not establish connection\") || error.message?.includes(\"MetaMask\") || error.message?.includes(\"chrome-extension\") || error.message?.includes(\"contentscript\") || error.message?.includes(\"serviceWorker\");\n        if (!isExtensionError) {\n            // Only log non-extension errors\n            // eslint-disable-next-line no-console\n            console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            // Check if it's an extension error\n            const isExtensionError = this.state.error?.message?.includes(\"Frame with ID\") || this.state.error?.message?.includes(\"Could not establish connection\") || this.state.error?.message?.includes(\"MetaMask\") || this.state.error?.message?.includes(\"chrome-extension\") || this.state.error?.message?.includes(\"contentscript\") || this.state.error?.message?.includes(\"serviceWorker\");\n            if (isExtensionError) {\n                // For extension errors, just render children normally\n                return this.props.children;\n            }\n            // For actual app errors, show fallback UI\n            return this.props.fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                            children: \"We're sorry, but something unexpected happened. Please refresh the page.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>window.location.reload(),\n                            className: \"btn-primary\",\n                            children: \"Refresh Page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "./src/components/ThemeController/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/ThemeController/index.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeController: () => (/* binding */ ThemeController),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_CommandLineIcon_SparklesIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,CommandLineIcon,SparklesIcon,SwatchIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChevronDownIcon,CommandLineIcon,SparklesIcon,SwatchIcon,XMarkIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ ThemeController,default auto */ \n\n\n\n\nconst ThemeController = ({ showInProduction = false, position = \"bottom-right\" })=>{\n    const { switchTheme, isGoldTheme, isPurpleTheme } = (0,_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if we should show the controller\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const isDevelopment = \"development\" === \"development\";\n        setIsVisible(isDevelopment || showInProduction);\n    }, [\n        showInProduction\n    ]);\n    // Don't render in production unless explicitly enabled\n    if (!isVisible) return null;\n    // Position classes\n    const positionClasses = {\n        \"bottom-right\": \"bottom-6 right-6\",\n        \"bottom-left\": \"bottom-6 left-6\",\n        \"top-right\": \"top-6 right-6\",\n        \"top-left\": \"top-6 left-6\"\n    };\n    const handleThemeSwitch = (theme)=>{\n        switchTheme(theme);\n        // Auto-close after selection\n        setTimeout(()=>setIsOpen(false), 500);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed ${positionClasses[position]} z-[9999] select-none`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20,\n                        scale: 0.9\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: 20,\n                        scale: 0.9\n                    },\n                    transition: {\n                        duration: 0.2,\n                        ease: \"easeOut\"\n                    },\n                    className: \"mb-4 p-4 rounded-2xl overflow-hidden\",\n                    style: {\n                        background: \"linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%)\",\n                        backdropFilter: \"blur(25px)\",\n                        WebkitBackdropFilter: \"blur(25px)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                        boxShadow: \"0 25px 50px rgba(0, 0, 0, 0.5)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_CommandLineIcon_SparklesIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.SwatchIcon, {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-sm\",\n                                            children: \"Theme Controller\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsOpen(false),\n                                    className: \"text-white/60 hover:text-white transition-colors p-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_CommandLineIcon_SparklesIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.XMarkIcon, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                    onClick: ()=>handleThemeSwitch(\"gold\"),\n                                    className: `w-full p-3 rounded-xl transition-all duration-300 group ${isGoldTheme ? \"ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-500/20 to-orange-500/20\" : \"hover:bg-white/5\"}`,\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-lg overflow-hidden\",\n                                                        style: {\n                                                            background: \"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)\",\n                                                            boxShadow: \"0 4px 12px rgba(255, 215, 0, 0.3)\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    isGoldTheme && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_CommandLineIcon_SparklesIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.SparklesIcon, {\n                                                            className: \"w-4 h-4 text-yellow-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium text-sm\",\n                                                        children: \"Gold Premium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60 text-xs\",\n                                                        children: \"Luxury & Elegance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            isGoldTheme && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_CommandLineIcon_SparklesIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CommandLineIcon, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                    onClick: ()=>handleThemeSwitch(\"purple\"),\n                                    className: `w-full p-3 rounded-xl transition-all duration-300 group ${isPurpleTheme ? \"ring-2 ring-purple-400 bg-gradient-to-r from-purple-500/20 to-blue-500/20\" : \"hover:bg-white/5\"}`,\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-lg overflow-hidden\",\n                                                        style: {\n                                                            background: \"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)\",\n                                                            boxShadow: \"0 4px 12px rgba(217, 70, 239, 0.3)\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    isPurpleTheme && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_CommandLineIcon_SparklesIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.SparklesIcon, {\n                                                            className: \"w-4 h-4 text-purple-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium text-sm\",\n                                                        children: \"Purple Dark\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60 text-xs\",\n                                                        children: \"Modern & Professional\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            isPurpleTheme && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-purple-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_CommandLineIcon_SparklesIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CommandLineIcon, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-3 border-t border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/40 text-xs text-center\",\n                                children: [\n                                    \"Press \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                        className: \"px-1 py-0.5 bg-white/10 rounded text-white/60\",\n                                        children: \"Ctrl+Shift+T\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 23\n                                    }, undefined),\n                                    \" to toggle\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-3 rounded-full overflow-hidden group\",\n                style: {\n                    background: isGoldTheme ? \"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)\" : \"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)\",\n                    boxShadow: isGoldTheme ? \"0 8px 25px rgba(255, 215, 0, 0.4)\" : \"0 8px 25px rgba(217, 70, 239, 0.4)\"\n                },\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                animate: {\n                    rotate: isOpen ? 180 : 0\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                        style: {\n                            background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                            backgroundSize: \"200% 100%\",\n                            animation: \"shimmer 2s ease-in-out infinite\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_CommandLineIcon_SparklesIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronDownIcon, {\n                            className: \"w-6 h-6 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_CommandLineIcon_SparklesIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.SwatchIcon, {\n                            className: \"w-6 h-6 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-1 -right-1 w-3 h-3 rounded-full bg-white shadow-lg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                children: !isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        x: -10\n                    },\n                    className: \"absolute right-full mr-3 top-1/2 -translate-y-1/2 px-2 py-1 rounded-md text-xs text-white whitespace-nowrap pointer-events-none\",\n                    style: {\n                        background: \"rgba(0, 0, 0, 0.8)\",\n                        backdropFilter: \"blur(10px)\"\n                    },\n                    children: isGoldTheme ? \"Gold Theme\" : \"Purple Theme\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ThemeController\\\\index.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ThemeController);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ThemeController/index.tsx\n");

/***/ }),

/***/ "./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst SessionProvider = ({ children, session })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        session: session,\n        refetchInterval: 5 * 60,\n        refetchOnWindowFocus: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\providers\\\\SessionProvider.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SessionProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUNtRDtBQVE3RSxNQUFNQyxrQkFBa0QsQ0FBQyxFQUN2REUsUUFBUSxFQUNSQyxPQUFPLEVBQ1I7SUFDQyxxQkFDRSw4REFBQ0YsNERBQXVCQTtRQUN0QkUsU0FBU0E7UUFDVEMsaUJBQWlCLElBQUk7UUFDckJDLHNCQUFzQjtrQkFFckJIOzs7Ozs7QUFHUDtBQUVBLGlFQUFlRixlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZyZWVsYS9sYW5kaW5nLXBhZ2UvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeD81YTgyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgYXMgTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xuaW1wb3J0IHR5cGUgeyBTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoJztcblxuaW50ZXJmYWNlIFNlc3Npb25Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgc2Vzc2lvbj86IFNlc3Npb24gfCBudWxsO1xufVxuXG5jb25zdCBTZXNzaW9uUHJvdmlkZXI6IFJlYWN0LkZDPFNlc3Npb25Qcm92aWRlclByb3BzPiA9ICh7IFxuICBjaGlsZHJlbiwgXG4gIHNlc3Npb24gXG59KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIFxuICAgICAgc2Vzc2lvbj17c2Vzc2lvbn1cbiAgICAgIHJlZmV0Y2hJbnRlcnZhbD17NSAqIDYwfSAvLyBSZWZldGNoIHNlc3Npb24gZXZlcnkgNSBtaW51dGVzXG4gICAgICByZWZldGNoT25XaW5kb3dGb2N1cz17dHJ1ZX1cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9OZXh0QXV0aFNlc3Npb25Qcm92aWRlcj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFNlc3Npb25Qcm92aWRlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNlc3Npb25Qcm92aWRlciIsIk5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iLCJzZXNzaW9uIiwicmVmZXRjaEludGVydmFsIiwicmVmZXRjaE9uV2luZG93Rm9jdXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Noto_Sans_Arabic_arguments_subsets_arabic_variable_font_arabic_display_swap_variableName_notoSansArabic___WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Noto_Sans_Arabic\",\"arguments\":[{\"subsets\":[\"arabic\"],\"variable\":\"--font-arabic\",\"display\":\"swap\"}],\"variableName\":\"notoSansArabic\"} */ \"../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Noto_Sans_Arabic\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\"],\\\"variable\\\":\\\"--font-arabic\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansArabic\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Noto_Sans_Arabic_arguments_subsets_arabic_variable_font_arabic_display_swap_variableName_notoSansArabic___WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Noto_Sans_Arabic_arguments_subsets_arabic_variable_font_arabic_display_swap_variableName_notoSansArabic___WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_display_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-display\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-display\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_display_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_display_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_500_600_700_800_900_variable_font_cairo_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"} */ \"../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-cairo\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_500_600_700_800_900_variable_font_cairo_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_500_600_700_800_900_variable_font_cairo_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"} */ \"../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Tajawal\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-tajawal\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"tajawal\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/providers/SessionProvider */ \"./src/components/providers/SessionProvider.tsx\");\n/* harmony import */ var _utils_errorFilter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/errorFilter */ \"./src/utils/errorFilter.ts\");\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n/* harmony import */ var _components_ThemeController__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ThemeController */ \"./src/components/ThemeController/index.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_11__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__, _components_ThemeController__WEBPACK_IMPORTED_MODULE_10__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__, _components_ThemeController__WEBPACK_IMPORTED_MODULE_10__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction App({ Component, pageProps: { session, ...pageProps } }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        // Set document direction based on locale\n        document.documentElement.dir = isRTL ? \"rtl\" : \"ltr\";\n        document.documentElement.lang = locale || \"ar\";\n        // Initialize error filtering for extension errors\n        (0,_utils_errorFilter__WEBPACK_IMPORTED_MODULE_8__.initializeErrorFilter)();\n    }, [\n        locale,\n        isRTL\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `${(next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_12___default().variable)} ${(next_font_google_target_css_path_src_pages_app_tsx_import_Noto_Sans_Arabic_arguments_subsets_arabic_variable_font_arabic_display_swap_variableName_notoSansArabic___WEBPACK_IMPORTED_MODULE_13___default().variable)} ${(next_font_google_target_css_path_src_pages_app_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_display_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_14___default().variable)} ${(next_font_google_target_css_path_src_pages_app_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_500_600_700_800_900_variable_font_cairo_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_15___default().variable)} ${(next_font_google_target_css_path_src_pages_app_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_16___default().variable)} ${isRTL ? \"font-cairo\" : \"font-sans\"}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                session: session,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_themes__WEBPACK_IMPORTED_MODULE_9__.ThemeProvider, {\n                    defaultTheme: \"gold\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                        attribute: \"class\",\n                        defaultTheme: \"dark\",\n                        enableSystem: false,\n                        disableTransitionOnChange: false,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                ...pageProps\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeController__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                                position: isRTL ? \"top-left\" : \"top-right\",\n                                toastOptions: {\n                                    duration: 4000,\n                                    style: {\n                                        background: \"var(--toast-bg)\",\n                                        color: \"var(--toast-color)\",\n                                        direction: isRTL ? \"rtl\" : \"ltr\"\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.appWithTranslation)(App));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:site_name\",\n                        content: \"Freela Syria\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:site\",\n                        content: \"@freela_syria\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/pages/chat-test.tsx":
/*!*********************************!*\
  !*** ./src/pages/chat-test.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n// Message Bubble Component\nconst MessageBubble = ({ message, isLast })=>{\n    const isUser = message.role === \"user\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: `flex ${isUser ? \"justify-end\" : \"justify-start\"} mb-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex items-end space-x-2 rtl:space-x-reverse max-w-[80%]`,\n            children: [\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `\n            px-4 py-3 rounded-2xl shadow-sm backdrop-blur-sm\n            ${isUser ? \"bg-primary-500 text-white rounded-br-md\" : \"bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-600\"}\n          `,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed whitespace-pre-wrap font-cairo\",\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-xs mt-2 ${isUser ? \"text-primary-100\" : \"text-gray-500 dark:text-gray-400\"}`,\n                            children: new Date(message.timestamp).toLocaleTimeString(\"ar-SA\", {\n                                hour: \"2-digit\",\n                                minute: \"2-digit\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n// Typing Indicator Component\nconst TypingIndicator = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 10\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -10\n        },\n        className: \"flex justify-start mb-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-2 rtl:space-x-reverse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-700 rounded-2xl rounded-bl-md px-4 py-3 border border-gray-200 dark:border-gray-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0.1s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0.2s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n// Main Chat Test Component\nconst ChatTestPage = ()=>{\n    const { currentTheme } = (0,_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // State management\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSending, setIsSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInitializing, setIsInitializing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [testMode, setTestMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isTyping\n    ]);\n    // Focus input on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        inputRef.current?.focus();\n    }, []);\n    // Initialize AI session when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeSession = async ()=>{\n            if (status === \"loading\") return;\n            if (!session?.user) {\n                setIsInitializing(false);\n                return;\n            }\n            try {\n                setIsInitializing(true);\n                setError(null);\n                // Start a new AI conversation session\n                const response = await fetch(\"/api/ai/conversation/start\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        userRole: \"EXPERT\",\n                        language: \"ar\",\n                        sessionType: \"onboarding\",\n                        culturalContext: {\n                            location: \"Syria\",\n                            dialect: \"general\"\n                        }\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(`Failed to start conversation: ${response.status}`);\n                }\n                const data = await response.json();\n                if (data.success && data.data) {\n                    const sessionData = {\n                        id: data.data.sessionId,\n                        currentStep: data.data.currentStep,\n                        status: data.data.status,\n                        userRole: \"EXPERT\",\n                        messages: data.data.messages || [],\n                        extractedData: data.data.extractedData || {},\n                        completionRate: data.data.completionRate || 0\n                    };\n                    setCurrentSession(sessionData);\n                    setMessages(sessionData.messages);\n                } else {\n                    throw new Error(data.error || \"Failed to initialize session\");\n                }\n            } catch (error) {\n                console.error(\"Failed to initialize AI session:\", error);\n                setError(error.message);\n                // Fall back to test mode\n                setTestMode(true);\n                initializeTestMode();\n            } finally{\n                setIsInitializing(false);\n            }\n        };\n        initializeSession();\n    }, [\n        session,\n        status\n    ]);\n    // Initialize test mode with mock data\n    const initializeTestMode = ()=>{\n        const mockMessages = [\n            {\n                id: \"1\",\n                role: \"assistant\",\n                content: \"مرحباً بك في فريلا سوريا! أنا هنا لمساعدتك في إعداد ملفك الشخصي. هل أنت خبير تريد تقديم خدماتك أم عميل تبحث عن خدمات؟\",\n                timestamp: new Date(Date.now() - 300000).toISOString(),\n                confidence: 0.95\n            },\n            {\n                id: \"2\",\n                role: \"user\",\n                content: \"أنا خبير في تطوير المواقع الإلكترونية\",\n                timestamp: new Date(Date.now() - 240000).toISOString()\n            },\n            {\n                id: \"3\",\n                role: \"assistant\",\n                content: \"ممتاز! تطوير المواقع الإلكترونية مجال مطلوب جداً. يمكنك أن تخبرني أكثر عن خبراتك؟ مثلاً، ما هي التقنيات التي تتقنها؟ React، Vue، Angular؟\",\n                timestamp: new Date(Date.now() - 180000).toISOString(),\n                confidence: 0.92\n            }\n        ];\n        const mockSession = {\n            id: \"test-session-1\",\n            currentStep: \"skills_assessment\",\n            status: \"active\",\n            userRole: \"EXPERT\",\n            messages: mockMessages,\n            extractedData: {},\n            completionRate: 0.3\n        };\n        setCurrentSession(mockSession);\n        setMessages(mockMessages);\n    };\n    // Real send message function with API integration\n    const sendMessage = async ()=>{\n        if (!inputValue.trim() || isSending) return;\n        const userMessage = {\n            id: `user_${Date.now()}`,\n            role: \"user\",\n            content: inputValue.trim(),\n            timestamp: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsSending(true);\n        setIsTyping(true);\n        setError(null);\n        try {\n            if (testMode || !currentSession) {\n                // Fall back to mock response in test mode\n                setTimeout(()=>{\n                    const aiResponses = [\n                        \"شكراً لك على هذه المعلومات المفيدة!\",\n                        \"هذا رائع! يمكنك أن تخبرني أكثر عن خبراتك؟\",\n                        \"ممتاز! ما هي المشاريع التي عملت عليها مؤخراً؟\",\n                        \"أفهم. هل لديك أمثلة على أعمالك السابقة؟\",\n                        \"رائع! كم سنة من الخبرة لديك في هذا المجال؟\"\n                    ];\n                    const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];\n                    const aiMessage = {\n                        id: `ai_${Date.now()}`,\n                        role: \"assistant\",\n                        content: randomResponse,\n                        timestamp: new Date().toISOString(),\n                        confidence: Math.random() * 0.3 + 0.7\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            aiMessage\n                        ]);\n                    setIsTyping(false);\n                    setIsSending(false);\n                }, 2000);\n                return;\n            }\n            // Real API call\n            const response = await fetch(\"/api/ai/conversation/message\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    sessionId: currentSession.id,\n                    message: inputValue.trim(),\n                    messageType: \"text\"\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`API request failed: ${response.status}`);\n            }\n            const data = await response.json();\n            if (data.success && data.data) {\n                const aiMessage = {\n                    id: data.data.aiMessage.id,\n                    role: \"assistant\",\n                    content: data.data.aiMessage.content,\n                    timestamp: data.data.aiMessage.timestamp,\n                    confidence: data.data.aiMessage.confidence,\n                    extractedData: data.data.aiMessage.extractedData\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n                // Update session data\n                if (currentSession) {\n                    setCurrentSession({\n                        ...currentSession,\n                        messages: [\n                            ...messages,\n                            userMessage,\n                            aiMessage\n                        ],\n                        extractedData: {\n                            ...currentSession.extractedData,\n                            ...data.data.extractedData\n                        },\n                        completionRate: data.data.completionRate\n                    });\n                }\n                // Check if conversation is completed\n                if (data.data.isCompleted) {\n                    // Handle completion - could redirect to dashboard\n                    console.log(\"Conversation completed!\", data.data.extractedData);\n                }\n            } else {\n                throw new Error(data.error || \"Failed to get AI response\");\n            }\n        } catch (error) {\n            console.error(\"Failed to send message:\", error);\n            setError(error.message);\n            // Add error message to chat\n            const errorMessage = {\n                id: `error_${Date.now()}`,\n                role: \"assistant\",\n                content: \"عذراً، حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.\",\n                timestamp: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsTyping(false);\n            setIsSending(false);\n        }\n    };\n    // Handle Enter key press\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    // Authentication check\n    if (status === \"loading\" || isInitializing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo flex items-center justify-center\",\n            dir: \"rtl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-theme rounded-2xl p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-white mb-2\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: status === \"loading\" ? \"جاري التحقق من الجلسة...\" : \"جاري تهيئة المحادثة الذكية...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                lineNumber: 401,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 400,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!session?.user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo flex items-center justify-center\",\n            dir: \"rtl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-theme rounded-2xl p-8 text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"تسجيل الدخول مطلوب\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-6\",\n                        children: \"يجب تسجيل الدخول للوصول إلى واجهة الدردشة الذكية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signIn)(\"google\"),\n                        className: \"w-full px-6 py-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 transition-colors font-semibold\",\n                        children: \"تسجيل الدخول بجوجل\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>{\n                            setTestMode(true);\n                            initializeTestMode();\n                        },\n                        className: \"w-full mt-3 px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors\",\n                        children: \"متابعة في وضع الاختبار\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                lineNumber: 415,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 414,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-white mb-2\",\n                            children: testMode ? \"اختبار واجهة الدردشة الذكية\" : \"الدردشة الذكية - فريلا سوريا\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: testMode ? \"وضع الاختبار - بيانات وهمية\" : \"محادثة حقيقية مع الذكاء الاصطناعي لإعداد ملفك الشخصي\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 11\n                        }, undefined),\n                        session?.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mt-2\",\n                            children: [\n                                \"مرحباً \",\n                                session.user.name,\n                                \" (\",\n                                session.user.email,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-xl text-red-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold mb-2\",\n                            children: \"حدث خطأ:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>{\n                                setError(null);\n                                setTestMode(true);\n                                initializeTestMode();\n                            },\n                            className: \"mt-3 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors\",\n                            children: \"التبديل إلى وضع الاختبار\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-theme rounded-2xl overflow-hidden shadow-theme-lg max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary-500/10 to-primary-600/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"مساعد فريلا الذكي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"متصل الآن\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            currentSession ? Math.round(currentSession.completionRate * 100) : 0,\n                                            \"% مكتمل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-96 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                            children: [\n                                messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBubble, {\n                                        message: message,\n                                        isLast: index === messages.length - 1\n                                    }, message.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                                    children: isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingIndicator, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 28\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end space-x-3 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: inputRef,\n                                            value: inputValue,\n                                            onChange: (e)=>setInputValue(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"اكتب رسالتك هنا...\",\n                                            className: \" w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none max-h-32 font-cairo placeholder-gray-500 dark:placeholder-gray-400 \",\n                                            rows: 1,\n                                            disabled: isSending\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: sendMessage,\n                                        disabled: !inputValue.trim() || isSending,\n                                        className: \" p-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex-shrink-0 \",\n                                        children: isSending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-theme rounded-xl p-4 max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"عناصر التحكم في الاختبار\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setIsTyping(!isTyping),\n                                        className: \"w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\",\n                                        children: [\n                                            isTyping ? \"إيقاف\" : \"تشغيل\",\n                                            \" مؤشر الكتابة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            if (testMode) {\n                                                initializeTestMode();\n                                            } else if (currentSession) {\n                                                setMessages(currentSession.messages);\n                                            }\n                                        },\n                                        className: \"w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\",\n                                        children: \"إعادة تعيين الرسائل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !testMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setTestMode(true);\n                                            initializeTestMode();\n                                        },\n                                        className: \"w-full px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors\",\n                                        children: \"التبديل إلى وضع الاختبار\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    testMode && session?.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setTestMode(false);\n                                            setError(null);\n                                            // Re-initialize real session\n                                            window.location.reload();\n                                        },\n                                        className: \"w-full px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors\",\n                                        children: \"العودة إلى الوضع الحقيقي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 444,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 443,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatTestPage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/chat-test.tsx\n");

/***/ }),

/***/ "./src/themes/gold-theme.ts":
/*!**********************************!*\
  !*** ./src/themes/gold-theme.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   goldTheme: () => (/* binding */ goldTheme),\n/* harmony export */   goldThemeCSSProperties: () => (/* binding */ goldThemeCSSProperties)\n/* harmony export */ });\nconst goldTheme = {\n    name: \"gold\",\n    displayName: \"Gold Premium\",\n    colors: {\n        primary: {\n            50: \"#fffbeb\",\n            100: \"#fef3c7\",\n            200: \"#fde68a\",\n            300: \"#fcd34d\",\n            400: \"#fbbf24\",\n            500: \"#f59e0b\",\n            600: \"#d97706\",\n            700: \"#b45309\",\n            800: \"#92400e\",\n            900: \"#78350f\",\n            950: \"#451a03\"\n        },\n        secondary: {\n            50: \"#fefce8\",\n            100: \"#fef9c3\",\n            200: \"#fef08a\",\n            300: \"#fde047\",\n            400: \"#facc15\",\n            500: \"#eab308\",\n            600: \"#ca8a04\",\n            700: \"#a16207\",\n            800: \"#854d0e\",\n            900: \"#713f12\",\n            950: \"#422006\"\n        },\n        accent: {\n            50: \"#fff7ed\",\n            100: \"#ffedd5\",\n            200: \"#fed7aa\",\n            300: \"#fdba74\",\n            400: \"#fb923c\",\n            500: \"#f97316\",\n            600: \"#ea580c\",\n            700: \"#c2410c\",\n            800: \"#9a3412\",\n            900: \"#7c2d12\",\n            950: \"#431407\"\n        },\n        neutral: {\n            50: \"#fafafa\",\n            100: \"#f5f5f5\",\n            200: \"#e5e5e5\",\n            300: \"#d4d4d4\",\n            400: \"#a3a3a3\",\n            500: \"#737373\",\n            600: \"#525252\",\n            700: \"#404040\",\n            800: \"#262626\",\n            900: \"#171717\",\n            950: \"#0a0a0a\"\n        },\n        glass: {\n            background: \"rgba(255, 215, 0, 0.12)\",\n            border: \"rgba(255, 215, 0, 0.25)\",\n            shadow: \"0 8px 32px rgba(255, 215, 0, 0.15)\",\n            backdropBlur: \"blur(25px)\"\n        },\n        text: {\n            primary: \"#ffffff\",\n            secondary: \"rgba(255, 255, 255, 0.8)\",\n            accent: \"#FFD700\",\n            muted: \"rgba(255, 255, 255, 0.6)\"\n        }\n    },\n    backgrounds: {\n        primary: `\n      radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.08) 0%, transparent 60%),\n      linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)\n    `,\n        secondary: `\n      radial-gradient(ellipse at top, rgba(184, 134, 11, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\n    `,\n        tertiary: `\n      radial-gradient(ellipse at bottom, rgba(139, 105, 20, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)\n    `\n    },\n    gradients: {\n        primary: \"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)\",\n        secondary: \"linear-gradient(135deg, #FFA500 0%, #FF8C00 100%)\",\n        accent: \"linear-gradient(135deg, #DAA520 0%, #B8860B 100%)\",\n        text: `linear-gradient(90deg,\n      #8B6914 0%, #B8860B 10%, #DAA520 20%, #FFD700 30%,\n      #FFED4E 40%, #FFF8DC 50%, #FFED4E 60%, #FFD700 70%,\n      #DAA520 80%, #B8860B 90%, #8B6914 100%)`,\n        button: \"linear-gradient(135deg, #FFD700 0%, #B8860B 50%, #DAA520 100%)\",\n        card: \"linear-gradient(135deg, rgba(255, 215, 0, 0.08) 0%, rgba(255, 215, 0, 0.04) 100%)\"\n    },\n    shadows: {\n        sm: \"0 2px 4px rgba(255, 215, 0, 0.1)\",\n        md: \"0 4px 8px rgba(255, 215, 0, 0.15)\",\n        lg: \"0 8px 16px rgba(255, 215, 0, 0.2)\",\n        xl: \"0 12px 24px rgba(255, 215, 0, 0.25)\",\n        glass: \"0 8px 32px rgba(255, 215, 0, 0.15)\",\n        premium: \"0 25px 50px rgba(255, 215, 0, 0.3)\"\n    },\n    animations: {\n        shimmer: `\n      @keyframes goldShimmer {\n        0%, 100% { background-position: 0% 50%; }\n        50% { background-position: 100% 50%; }\n      }\n    `,\n        glow: `\n      @keyframes goldGlow {\n        0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }\n        50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }\n      }\n    `,\n        pulse: `\n      @keyframes goldPulse {\n        0%, 100% { opacity: 1; }\n        50% { opacity: 0.7; }\n      }\n    `,\n        float: `\n      @keyframes goldFloat {\n        0%, 100% { transform: translateY(0px); }\n        50% { transform: translateY(-10px); }\n      }\n    `\n    }\n};\n// Gold theme CSS custom properties\nconst goldThemeCSSProperties = {\n    // Primary colors\n    \"--theme-primary-50\": goldTheme.colors.primary[50],\n    \"--theme-primary-100\": goldTheme.colors.primary[100],\n    \"--theme-primary-200\": goldTheme.colors.primary[200],\n    \"--theme-primary-300\": goldTheme.colors.primary[300],\n    \"--theme-primary-400\": goldTheme.colors.primary[400],\n    \"--theme-primary-500\": goldTheme.colors.primary[500],\n    \"--theme-primary-600\": goldTheme.colors.primary[600],\n    \"--theme-primary-700\": goldTheme.colors.primary[700],\n    \"--theme-primary-800\": goldTheme.colors.primary[800],\n    \"--theme-primary-900\": goldTheme.colors.primary[900],\n    // Glass effects\n    \"--theme-glass-bg\": goldTheme.colors.glass.background,\n    \"--theme-glass-border\": goldTheme.colors.glass.border,\n    \"--theme-glass-shadow\": goldTheme.colors.glass.shadow,\n    \"--theme-glass-blur\": goldTheme.colors.glass.backdropBlur,\n    // Backgrounds\n    \"--theme-bg-primary\": goldTheme.backgrounds.primary,\n    \"--theme-bg-secondary\": goldTheme.backgrounds.secondary,\n    \"--theme-bg-tertiary\": goldTheme.backgrounds.tertiary || goldTheme.backgrounds.secondary,\n    // Text colors\n    \"--theme-text-primary\": goldTheme.colors.text.primary,\n    \"--theme-text-secondary\": goldTheme.colors.text.secondary,\n    \"--theme-text-accent\": goldTheme.colors.text.accent,\n    \"--theme-text-muted\": goldTheme.colors.text.muted,\n    // Gradients\n    \"--theme-gradient-primary\": goldTheme.gradients.primary,\n    \"--theme-gradient-secondary\": goldTheme.gradients.secondary,\n    \"--theme-gradient-accent\": goldTheme.gradients.accent,\n    \"--theme-gradient-text\": goldTheme.gradients.text,\n    \"--theme-gradient-button\": goldTheme.gradients.button,\n    \"--theme-gradient-card\": goldTheme.gradients.card,\n    // Shadows\n    \"--theme-shadow-sm\": goldTheme.shadows.sm,\n    \"--theme-shadow-md\": goldTheme.shadows.md,\n    \"--theme-shadow-lg\": goldTheme.shadows.lg,\n    \"--theme-shadow-xl\": goldTheme.shadows.xl,\n    \"--theme-shadow-glass\": goldTheme.shadows.glass,\n    \"--theme-shadow-premium\": goldTheme.shadows.premium\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/themes/gold-theme.ts\n");

/***/ }),

/***/ "./src/themes/index.tsx":
/*!******************************!*\
  !*** ./src/themes/index.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeAware: () => (/* binding */ ThemeAware),\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   applyThemeToDocument: () => (/* reexport safe */ _theme_utils__WEBPACK_IMPORTED_MODULE_2__.applyThemeToDocument),\n/* harmony export */   getStoredTheme: () => (/* reexport safe */ _theme_utils__WEBPACK_IMPORTED_MODULE_2__.getStoredTheme),\n/* harmony export */   getTheme: () => (/* reexport safe */ _theme_utils__WEBPACK_IMPORTED_MODULE_2__.getTheme),\n/* harmony export */   getThemeClasses: () => (/* reexport safe */ _theme_utils__WEBPACK_IMPORTED_MODULE_2__.getThemeClasses),\n/* harmony export */   goldTheme: () => (/* reexport safe */ _gold_theme__WEBPACK_IMPORTED_MODULE_3__.goldTheme),\n/* harmony export */   purpleTheme: () => (/* reexport safe */ _purple_theme__WEBPACK_IMPORTED_MODULE_4__.purpleTheme),\n/* harmony export */   storeTheme: () => (/* reexport safe */ _theme_utils__WEBPACK_IMPORTED_MODULE_2__.storeTheme),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   withTheme: () => (/* binding */ withTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _theme_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./theme-utils */ \"./src/themes/theme-utils.ts\");\n/* harmony import */ var _gold_theme__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./gold-theme */ \"./src/themes/gold-theme.ts\");\n/* harmony import */ var _purple_theme__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./purple-theme */ \"./src/themes/purple-theme.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,withTheme,ThemeAware,getTheme,getThemeClasses,applyThemeToDocument,storeTheme,getStoredTheme,goldTheme,purpleTheme auto */ \n\n\n// Create theme context\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Theme provider component\nconst ThemeProvider = ({ children, defaultTheme = \"gold\" })=>{\n    const [themeName, setThemeName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initialTheme = (0,_theme_utils__WEBPACK_IMPORTED_MODULE_2__.initializeTheme)(defaultTheme);\n        setThemeName(initialTheme);\n        setIsInitialized(true);\n    }, [\n        defaultTheme\n    ]);\n    // Get current theme configuration\n    const currentTheme = (0,_theme_utils__WEBPACK_IMPORTED_MODULE_2__.getTheme)(themeName);\n    // Switch theme function\n    const switchTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newTheme)=>{\n        if (newTheme === themeName) return;\n        // Enable smooth transitions\n        (0,_theme_utils__WEBPACK_IMPORTED_MODULE_2__.enableThemeTransitions)();\n        // Apply new theme\n        (0,_theme_utils__WEBPACK_IMPORTED_MODULE_2__.applyThemeToDocument)(newTheme);\n        (0,_theme_utils__WEBPACK_IMPORTED_MODULE_2__.injectThemeCSS)(newTheme);\n        // Update state\n        setThemeName(newTheme);\n        // Store in localStorage\n        (0,_theme_utils__WEBPACK_IMPORTED_MODULE_2__.storeTheme)(newTheme);\n        // Dispatch custom event for other components\n        if (false) {}\n    }, [\n        themeName\n    ]);\n    // Keyboard shortcut handler\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyboard = (event)=>{\n            // Ctrl+Shift+T to toggle theme\n            if (event.ctrlKey && event.shiftKey && event.key === \"T\") {\n                event.preventDefault();\n                const newTheme = themeName === \"gold\" ? \"purple\" : \"gold\";\n                switchTheme(newTheme);\n            }\n        };\n        if (false) {}\n    }, [\n        themeName,\n        switchTheme\n    ]);\n    // Context value\n    const contextValue = {\n        currentTheme,\n        themeName,\n        switchTheme,\n        isGoldTheme: themeName === \"gold\",\n        isPurpleTheme: themeName === \"purple\",\n        themeClasses: (0,_theme_utils__WEBPACK_IMPORTED_MODULE_2__.getThemeClasses)(themeName)\n    };\n    // Don't render until initialized to prevent hydration mismatch\n    if (!isInitialized) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `theme-provider ${(0,_theme_utils__WEBPACK_IMPORTED_MODULE_2__.getThemeClasses)(themeName)}`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\themes\\\\index.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\themes\\\\index.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n// Custom hook to use theme context\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\n// Higher-order component for theme-aware components\nconst withTheme = (Component)=>{\n    const ThemedComponent = (props)=>{\n        const theme = useTheme();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props,\n            theme: theme\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\themes\\\\index.tsx\",\n            lineNumber: 119,\n            columnNumber: 12\n        }, undefined);\n    };\n    ThemedComponent.displayName = `withTheme(${Component.displayName || Component.name})`;\n    return ThemedComponent;\n};\n// Theme-aware component wrapper\nconst ThemeAware = ({ children })=>{\n    const theme = useTheme();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children(theme)\n    }, void 0, false);\n};\n// Export theme utilities for direct use\n\n\n// Export theme configurations\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/themes/index.tsx\n");

/***/ }),

/***/ "./src/themes/purple-theme.ts":
/*!************************************!*\
  !*** ./src/themes/purple-theme.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   purpleTheme: () => (/* binding */ purpleTheme),\n/* harmony export */   purpleThemeCSSProperties: () => (/* binding */ purpleThemeCSSProperties)\n/* harmony export */ });\nconst purpleTheme = {\n    name: \"purple\",\n    displayName: \"Purple Dark\",\n    colors: {\n        primary: {\n            50: \"#fdf4ff\",\n            100: \"#fae8ff\",\n            200: \"#f5d0fe\",\n            300: \"#f0abfc\",\n            400: \"#e879f9\",\n            500: \"#d946ef\",\n            600: \"#c026d3\",\n            700: \"#a21caf\",\n            800: \"#86198f\",\n            900: \"#701a75\",\n            950: \"#4a044e\"\n        },\n        secondary: {\n            50: \"#f0f9ff\",\n            100: \"#e0f2fe\",\n            200: \"#bae6fd\",\n            300: \"#7dd3fc\",\n            400: \"#38bdf8\",\n            500: \"#0ea5e9\",\n            600: \"#0284c7\",\n            700: \"#0369a1\",\n            800: \"#075985\",\n            900: \"#0c4a6e\",\n            950: \"#082f49\"\n        },\n        accent: {\n            50: \"#eff6ff\",\n            100: \"#dbeafe\",\n            200: \"#bfdbfe\",\n            300: \"#93c5fd\",\n            400: \"#60a5fa\",\n            500: \"#3b82f6\",\n            600: \"#2563eb\",\n            700: \"#1d4ed8\",\n            800: \"#1e40af\",\n            900: \"#1e3a8a\",\n            950: \"#172554\"\n        },\n        neutral: {\n            50: \"#fafafa\",\n            100: \"#f5f5f5\",\n            200: \"#e5e5e5\",\n            300: \"#d4d4d4\",\n            400: \"#a3a3a3\",\n            500: \"#737373\",\n            600: \"#525252\",\n            700: \"#404040\",\n            800: \"#262626\",\n            900: \"#171717\",\n            950: \"#0a0a0a\"\n        },\n        glass: {\n            background: \"rgba(217, 70, 239, 0.12)\",\n            border: \"rgba(217, 70, 239, 0.25)\",\n            shadow: \"0 8px 32px rgba(217, 70, 239, 0.15)\",\n            backdropBlur: \"blur(25px)\"\n        },\n        text: {\n            primary: \"#ffffff\",\n            secondary: \"rgba(255, 255, 255, 0.8)\",\n            accent: \"#d946ef\",\n            muted: \"rgba(255, 255, 255, 0.6)\"\n        }\n    },\n    backgrounds: {\n        primary: `\n      radial-gradient(circle at 50% 50%, rgba(217, 70, 239, 0.08) 0%, transparent 60%),\n      linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)\n    `,\n        secondary: `\n      radial-gradient(ellipse at top, rgba(192, 38, 211, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\n    `,\n        tertiary: `\n      radial-gradient(ellipse at bottom, rgba(162, 28, 175, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)\n    `\n    },\n    gradients: {\n        primary: \"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)\",\n        secondary: \"linear-gradient(135deg, #c026d3 0%, #86198f 100%)\",\n        accent: \"linear-gradient(135deg, #e879f9 0%, #c026d3 100%)\",\n        text: `linear-gradient(90deg,\n      #701a75 0%, #86198f 10%, #a21caf 20%, #c026d3 30%,\n      #d946ef 40%, #e879f9 50%, #d946ef 60%, #c026d3 70%,\n      #a21caf 80%, #86198f 90%, #701a75 100%)`,\n        button: \"linear-gradient(135deg, #d946ef 0%, #a21caf 50%, #86198f 100%)\",\n        card: \"linear-gradient(135deg, rgba(217, 70, 239, 0.08) 0%, rgba(217, 70, 239, 0.04) 100%)\"\n    },\n    shadows: {\n        sm: \"0 2px 4px rgba(217, 70, 239, 0.1)\",\n        md: \"0 4px 8px rgba(217, 70, 239, 0.15)\",\n        lg: \"0 8px 16px rgba(217, 70, 239, 0.2)\",\n        xl: \"0 12px 24px rgba(217, 70, 239, 0.25)\",\n        glass: \"0 8px 32px rgba(217, 70, 239, 0.15)\",\n        premium: \"0 25px 50px rgba(217, 70, 239, 0.3)\"\n    },\n    animations: {\n        shimmer: `\n      @keyframes purpleShimmer {\n        0%, 100% { background-position: 0% 50%; }\n        50% { background-position: 100% 50%; }\n      }\n    `,\n        glow: `\n      @keyframes purpleGlow {\n        0%, 100% { box-shadow: 0 0 20px rgba(217, 70, 239, 0.3); }\n        50% { box-shadow: 0 0 40px rgba(217, 70, 239, 0.6); }\n      }\n    `,\n        pulse: `\n      @keyframes purplePulse {\n        0%, 100% { opacity: 1; }\n        50% { opacity: 0.7; }\n      }\n    `,\n        float: `\n      @keyframes purpleFloat {\n        0%, 100% { transform: translateY(0px); }\n        50% { transform: translateY(-10px); }\n      }\n    `\n    }\n};\n// Purple theme CSS custom properties\nconst purpleThemeCSSProperties = {\n    // Primary colors\n    \"--theme-primary-50\": purpleTheme.colors.primary[50],\n    \"--theme-primary-100\": purpleTheme.colors.primary[100],\n    \"--theme-primary-200\": purpleTheme.colors.primary[200],\n    \"--theme-primary-300\": purpleTheme.colors.primary[300],\n    \"--theme-primary-400\": purpleTheme.colors.primary[400],\n    \"--theme-primary-500\": purpleTheme.colors.primary[500],\n    \"--theme-primary-600\": purpleTheme.colors.primary[600],\n    \"--theme-primary-700\": purpleTheme.colors.primary[700],\n    \"--theme-primary-800\": purpleTheme.colors.primary[800],\n    \"--theme-primary-900\": purpleTheme.colors.primary[900],\n    // Glass effects\n    \"--theme-glass-bg\": purpleTheme.colors.glass.background,\n    \"--theme-glass-border\": purpleTheme.colors.glass.border,\n    \"--theme-glass-shadow\": purpleTheme.colors.glass.shadow,\n    \"--theme-glass-blur\": purpleTheme.colors.glass.backdropBlur,\n    // Backgrounds\n    \"--theme-bg-primary\": purpleTheme.backgrounds.primary,\n    \"--theme-bg-secondary\": purpleTheme.backgrounds.secondary,\n    \"--theme-bg-tertiary\": purpleTheme.backgrounds.tertiary || purpleTheme.backgrounds.secondary,\n    // Text colors\n    \"--theme-text-primary\": purpleTheme.colors.text.primary,\n    \"--theme-text-secondary\": purpleTheme.colors.text.secondary,\n    \"--theme-text-accent\": purpleTheme.colors.text.accent,\n    \"--theme-text-muted\": purpleTheme.colors.text.muted,\n    // Gradients\n    \"--theme-gradient-primary\": purpleTheme.gradients.primary,\n    \"--theme-gradient-secondary\": purpleTheme.gradients.secondary,\n    \"--theme-gradient-accent\": purpleTheme.gradients.accent,\n    \"--theme-gradient-text\": purpleTheme.gradients.text,\n    \"--theme-gradient-button\": purpleTheme.gradients.button,\n    \"--theme-gradient-card\": purpleTheme.gradients.card,\n    // Shadows\n    \"--theme-shadow-sm\": purpleTheme.shadows.sm,\n    \"--theme-shadow-md\": purpleTheme.shadows.md,\n    \"--theme-shadow-lg\": purpleTheme.shadows.lg,\n    \"--theme-shadow-xl\": purpleTheme.shadows.xl,\n    \"--theme-shadow-glass\": purpleTheme.shadows.glass,\n    \"--theme-shadow-premium\": purpleTheme.shadows.premium\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/themes/purple-theme.ts\n");

/***/ }),

/***/ "./src/themes/theme-utils.ts":
/*!***********************************!*\
  !*** ./src/themes/theme-utils.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyThemeToDocument: () => (/* binding */ applyThemeToDocument),\n/* harmony export */   disableThemeTransitions: () => (/* binding */ disableThemeTransitions),\n/* harmony export */   enableThemeTransitions: () => (/* binding */ enableThemeTransitions),\n/* harmony export */   generateThemeCSS: () => (/* binding */ generateThemeCSS),\n/* harmony export */   getOppositeTheme: () => (/* binding */ getOppositeTheme),\n/* harmony export */   getStoredTheme: () => (/* binding */ getStoredTheme),\n/* harmony export */   getTheme: () => (/* binding */ getTheme),\n/* harmony export */   getThemeCSSProperties: () => (/* binding */ getThemeCSSProperties),\n/* harmony export */   getThemeClasses: () => (/* binding */ getThemeClasses),\n/* harmony export */   initializeTheme: () => (/* binding */ initializeTheme),\n/* harmony export */   injectThemeCSS: () => (/* binding */ injectThemeCSS),\n/* harmony export */   isValidTheme: () => (/* binding */ isValidTheme),\n/* harmony export */   storeTheme: () => (/* binding */ storeTheme),\n/* harmony export */   themeCSSProperties: () => (/* binding */ themeCSSProperties),\n/* harmony export */   themes: () => (/* binding */ themes)\n/* harmony export */ });\n/* harmony import */ var _gold_theme__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gold-theme */ \"./src/themes/gold-theme.ts\");\n/* harmony import */ var _purple_theme__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./purple-theme */ \"./src/themes/purple-theme.ts\");\n\n\n// Theme registry\nconst themes = {\n    gold: _gold_theme__WEBPACK_IMPORTED_MODULE_0__.goldTheme,\n    purple: _purple_theme__WEBPACK_IMPORTED_MODULE_1__.purpleTheme\n};\n// CSS Properties registry\nconst themeCSSProperties = {\n    gold: _gold_theme__WEBPACK_IMPORTED_MODULE_0__.goldThemeCSSProperties,\n    purple: _purple_theme__WEBPACK_IMPORTED_MODULE_1__.purpleThemeCSSProperties\n};\n// Get theme configuration by name\nconst getTheme = (themeName)=>{\n    return themes[themeName];\n};\n// Get CSS properties for theme\nconst getThemeCSSProperties = (themeName)=>{\n    return themeCSSProperties[themeName];\n};\n// Apply theme CSS properties to document root\nconst applyThemeToDocument = (themeName)=>{\n    const properties = getThemeCSSProperties(themeName);\n    const root = document.documentElement;\n    // Remove existing theme classes\n    root.classList.remove(\"theme-gold\", \"theme-purple\");\n    // Add new theme class\n    root.classList.add(`theme-${themeName}`);\n    // Apply CSS custom properties\n    Object.entries(properties).forEach(([property, value])=>{\n        root.style.setProperty(property, value);\n    });\n};\n// Get theme from localStorage\nconst getStoredTheme = ()=>{\n    if (true) return null;\n    try {\n        const stored = localStorage.getItem(\"freela-theme\");\n        return stored && (stored === \"gold\" || stored === \"purple\") ? stored : null;\n    } catch  {\n        return null;\n    }\n};\n// Store theme in localStorage\nconst storeTheme = (themeName)=>{\n    if (true) return;\n    try {\n        localStorage.setItem(\"freela-theme\", themeName);\n    } catch  {\n    // Silently fail if localStorage is not available\n    }\n};\n// Generate theme-aware CSS classes\nconst getThemeClasses = (themeName)=>{\n    return `theme-${themeName}`;\n};\n// Check if theme is valid\nconst isValidTheme = (theme)=>{\n    return theme === \"gold\" || theme === \"purple\";\n};\n// Get opposite theme\nconst getOppositeTheme = (themeName)=>{\n    return themeName === \"gold\" ? \"purple\" : \"gold\";\n};\n// Theme transition utilities\nconst enableThemeTransitions = ()=>{\n    if (typeof document === \"undefined\") return;\n    const style = document.createElement(\"style\");\n    style.textContent = `\n    * {\n      transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease !important;\n    }\n  `;\n    document.head.appendChild(style);\n    // Remove after transition completes\n    setTimeout(()=>{\n        document.head.removeChild(style);\n    }, 300);\n};\n// Disable theme transitions temporarily\nconst disableThemeTransitions = ()=>{\n    if (typeof document === \"undefined\") return;\n    const style = document.createElement(\"style\");\n    style.textContent = `\n    * {\n      transition: none !important;\n    }\n  `;\n    document.head.appendChild(style);\n    // Force reflow\n    document.body.offsetHeight;\n    // Remove immediately\n    document.head.removeChild(style);\n};\n// Generate dynamic CSS for theme\nconst generateThemeCSS = (themeName)=>{\n    const theme = getTheme(themeName);\n    const properties = getThemeCSSProperties(themeName);\n    return `\n    .theme-${themeName} {\n      ${Object.entries(properties).map(([property, value])=>`${property}: ${value};`).join(\"\\n      \")}\n    }\n    \n    /* Theme-specific animations */\n    ${theme.animations.shimmer}\n    ${theme.animations.glow}\n    ${theme.animations.pulse}\n    ${theme.animations.float}\n    \n    /* Theme-specific utilities */\n    .theme-${themeName} .bg-theme-primary {\n      background: var(--theme-bg-primary);\n    }\n    \n    .theme-${themeName} .bg-theme-secondary {\n      background: var(--theme-bg-secondary);\n    }\n    \n    .theme-${themeName} .glass-effect {\n      background: var(--theme-glass-bg);\n      border: 1px solid var(--theme-glass-border);\n      box-shadow: var(--theme-glass-shadow);\n      backdrop-filter: var(--theme-glass-blur);\n      -webkit-backdrop-filter: var(--theme-glass-blur);\n    }\n    \n    .theme-${themeName} .text-theme-gradient {\n      background: var(--theme-gradient-text);\n      background-size: 300% 100%;\n      -webkit-background-clip: text;\n      background-clip: text;\n      -webkit-text-fill-color: transparent;\n      animation: ${themeName}Shimmer 4s ease-in-out infinite;\n    }\n    \n    .theme-${themeName} .btn-theme-primary {\n      background: var(--theme-gradient-button);\n      box-shadow: var(--theme-shadow-premium);\n      color: white;\n      border: 1px solid var(--theme-glass-border);\n    }\n    \n    .theme-${themeName} .btn-theme-primary:hover {\n      animation: ${themeName}Glow 2s ease-in-out infinite;\n    }\n  `;\n};\n// Inject theme CSS into document\nconst injectThemeCSS = (themeName)=>{\n    if (typeof document === \"undefined\") return;\n    // Remove existing theme styles\n    const existingStyle = document.getElementById(`theme-${themeName}-styles`);\n    if (existingStyle) {\n        existingStyle.remove();\n    }\n    // Create new style element\n    const style = document.createElement(\"style\");\n    style.id = `theme-${themeName}-styles`;\n    style.textContent = generateThemeCSS(themeName);\n    // Append to head\n    document.head.appendChild(style);\n};\n// Initialize theme system\nconst initializeTheme = (defaultTheme = \"gold\")=>{\n    const storedTheme = getStoredTheme();\n    const initialTheme = storedTheme || defaultTheme;\n    // Apply theme to document\n    applyThemeToDocument(initialTheme);\n    // Inject theme CSS\n    injectThemeCSS(initialTheme);\n    return initialTheme;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/themes/theme-utils.ts\n");

/***/ }),

/***/ "./src/utils/errorFilter.ts":
/*!**********************************!*\
  !*** ./src/utils/errorFilter.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeErrorFilter: () => (/* binding */ initializeErrorFilter),\n/* harmony export */   restoreConsole: () => (/* binding */ restoreConsole)\n/* harmony export */ });\n/**\n * Filter out known browser extension errors that we can't control\n * These errors are from browser extensions and don't affect our application\n */ // Store original console methods\nconst originalConsoleError = console.error;\nconst originalConsoleWarn = console.warn;\n// Extension error patterns to filter out\nconst EXTENSION_ERROR_PATTERNS = [\n    /Frame with ID \\d+ was removed/,\n    /Could not establish connection\\. Receiving end does not exist/,\n    /MetaMask extension not found/,\n    /chrome-extension:/,\n    /contentscript/,\n    /serviceWorker/,\n    /background\\.js/,\n    /No tab with id:/,\n    /Unchecked runtime\\.lastError/,\n    /\\[ChromeTransport\\]/\n];\n/**\n * Check if an error message matches known extension error patterns\n */ function isExtensionError(message) {\n    return EXTENSION_ERROR_PATTERNS.some((pattern)=>pattern.test(message));\n}\n/**\n * Initialize error filtering to suppress extension errors in console\n */ function initializeErrorFilter() {\n    // Only filter in development mode\n    if (true) {\n        console.error = (...args)=>{\n            const message = args.join(\" \");\n            if (!isExtensionError(message)) {\n                originalConsoleError.apply(console, args);\n            }\n        };\n        console.warn = (...args)=>{\n            const message = args.join(\" \");\n            if (!isExtensionError(message)) {\n                originalConsoleWarn.apply(console, args);\n            }\n        };\n        // Filter unhandled promise rejections\n        window.addEventListener(\"unhandledrejection\", (event)=>{\n            const message = event.reason?.message || event.reason?.toString() || \"\";\n            if (isExtensionError(message)) {\n                event.preventDefault();\n            }\n        });\n        // Filter global errors\n        window.addEventListener(\"error\", (event)=>{\n            const message = event.message || \"\";\n            if (isExtensionError(message)) {\n                event.preventDefault();\n            }\n        });\n    }\n}\n/**\n * Restore original console methods (for testing or cleanup)\n */ function restoreConsole() {\n    console.error = originalConsoleError;\n    console.warn = originalConsoleWarn;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/errorFilter.ts\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-auth/react");

/***/ }),

/***/ "next-i18next":
/*!*******************************!*\
  !*** external "next-i18next" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-i18next");

/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat-test&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cchat-test.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();