# Database (Legacy PostgreSQL - being migrated to Supabase)
DATABASE_URL="postgresql://username:password@localhost:5432/freela_syria"

# Supabase Configuration
SUPABASE_URL="https://bivignfixaqrmdcbsnqh.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpdmlnbmZpeGFxcm1kY2JzbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MzY1MDYsImV4cCI6MjA2NTQxMjUwNn0.cMwSd8oFF5CDyXBaaqPL7EVHhF9l32ERd6krX4DAo4E"
SUPABASE_SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpdmlnbmZpeGFxcm1kY2JzbnFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTgzNjUwNiwiZXhwIjoyMDY1NDEyNTA2fQ.Ue6KVdG7c-iwZWKr4D-BhRzj82yp2b81uikFYXSdvZ8"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="freela-syria-development-jwt-secret-key-2024"
JWT_REFRESH_SECRET="freela-syria-development-refresh-secret-key-2024"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server
PORT=3005
NODE_ENV="development"
API_VERSION="v1"
CORS_ORIGIN="http://localhost:3000,http://localhost:3003,http://localhost:3006,http://localhost:19006"

# Email (Development - using console for now)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="development-password"
FROM_EMAIL="<EMAIL>"
FROM_NAME="Freela Syria"

# File Upload
UPLOAD_MAX_SIZE="10485760"
UPLOAD_ALLOWED_TYPES="jpg,jpeg,png,gif,webp,pdf,doc,docx,txt"
UPLOAD_PATH="uploads"
CDN_URL="http://localhost:3005/uploads"

# Rate Limiting
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="100"

# Security
BCRYPT_ROUNDS="12"
SESSION_SECRET="freela-syria-development-session-secret-2024"

# External APIs
OPENAI_API_KEY="your-openai-api-key"
OPENROUTER_API_KEY="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"
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="901570477030-to96008habtve92rcrkr12bbipjs236i.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# Monitoring
LOG_LEVEL="info"
SENTRY_DSN=""

# Payment (Development - disabled for now)
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""
PAYPAL_CLIENT_ID=""
PAYPAL_CLIENT_SECRET=""
